/**
 * Publish-Subscribe System Types
 * Comprehensive TypeScript definitions for the WebSocket pub-sub implementation
 */

// Core event types that can be subscribed to
export enum EventType {
  // Authentication events
  AUTH_SUCCESS = 'auth:success',
  AUTH_FAILED = 'auth:failed',

  // Account events
  BALANCE_UPDATE = 'account:balance',
  ACCOUNT_STATUS = 'account:status',

  // Trading events
  CHARTS_UPDATE = 'trading:charts',
  HISTORY_UPDATE = 'trading:history',
  HISTORY_LOAD = 'trading:history_load',
  STREAM_UPDATE = 'trading:stream',
  ASSETS_UPDATE = 'trading:assets',

  // System events
  CONNECTION_STATUS = 'system:connection',
  HEARTBEAT = 'system:heartbeat',
  ERROR = 'system:error',

  // Custom events (for extensibility)
  CUSTOM = 'custom'
}

// Event channels for grouping related events
export enum EventChannel {
  AUTHENTICATION = 'auth',
  ACCOUNT = 'account',
  TRADING = 'trading',
  SYSTEM = 'system',
  ALL = '*' // Special channel for subscribing to all events
}

// Client identifier type
export type ClientId = string

// Subscription identifier
export type SubscriptionId = string

// Event data payload (flexible for different event types)
export interface EventPayload {
  timestamp: number
  eventType: EventType
  channel: EventChannel
  data: unknown
  metadata?: Record<string, unknown>
}

// Subscription configuration
export interface SubscriptionConfig {
  clientId: ClientId
  eventType?: EventType
  channel?: EventChannel
  filter?: EventFilter
  priority?: SubscriptionPriority
}

// Event filtering options
export interface EventFilter {
  // Filter by specific data properties
  dataFilter?: Record<string, unknown>
  // Filter by metadata
  metadataFilter?: Record<string, unknown>
  // Custom filter function
  customFilter?: (payload: EventPayload) => boolean
}

// Subscription priority levels
export enum SubscriptionPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

// Subscription record for internal tracking
export interface Subscription {
  id: SubscriptionId
  clientId: ClientId
  eventType?: EventType
  channel?: EventChannel
  filter?: EventFilter
  priority: SubscriptionPriority
  createdAt: number
  lastActivity: number
}

// Client information for tracking
export interface ClientInfo {
  id: ClientId
  windowId: number
  subscriptions: Set<SubscriptionId>
  isActive: boolean
  connectedAt: number
  lastActivity: number
  metadata?: Record<string, unknown>
}

// Pub-sub operation results
export interface SubscriptionResult {
  success: boolean
  subscriptionId?: SubscriptionId
  error?: string
  message?: string
}

export interface PublishResult {
  success: boolean
  deliveredTo: ClientId[]
  failedDeliveries: ClientId[]
  totalSubscribers: number
  error?: string
}

export interface UnsubscribeResult {
  success: boolean
  removedSubscriptions: number
  error?: string
  message?: string
}

// Event statistics for monitoring
export interface EventStats {
  totalEvents: number
  eventsByType: Record<EventType, number>
  eventsByChannel: Record<EventChannel, number>
  activeSubscriptions: number
  activeClients: number
  averageDeliveryTime: number
}

// Configuration for the pub-sub system
export interface PubSubConfig {
  maxSubscriptionsPerClient: number
  maxClients: number
  clientTimeoutMs: number
  enableEventHistory: boolean
  eventHistorySize: number
  enableMetrics: boolean
  deliveryTimeoutMs: number
}

// Error types for pub-sub operations
export enum PubSubErrorType {
  CLIENT_NOT_FOUND = 'CLIENT_NOT_FOUND',
  SUBSCRIPTION_NOT_FOUND = 'SUBSCRIPTION_NOT_FOUND',
  MAX_SUBSCRIPTIONS_EXCEEDED = 'MAX_SUBSCRIPTIONS_EXCEEDED',
  MAX_CLIENTS_EXCEEDED = 'MAX_CLIENTS_EXCEEDED',
  INVALID_EVENT_TYPE = 'INVALID_EVENT_TYPE',
  INVALID_CHANNEL = 'INVALID_CHANNEL',
  DELIVERY_FAILED = 'DELIVERY_FAILED',
  CLIENT_TIMEOUT = 'CLIENT_TIMEOUT'
}

export class PubSubError extends Error {
  constructor(
    public type: PubSubErrorType,
    message: string,
    public details?: Record<string, unknown>
  ) {
    super(message)
    this.name = 'PubSubError'
  }
}

// Event history entry for debugging and replay
export interface EventHistoryEntry {
  id: string
  payload: EventPayload
  deliveredTo: ClientId[]
  deliveryTime: number
  createdAt: number
}

// Subscription matcher utility type
export type SubscriptionMatcher = (subscription: Subscription, payload: EventPayload) => boolean

// Event transformer for modifying payloads before delivery
export type EventTransformer = (payload: EventPayload, subscription: Subscription) => EventPayload

// Delivery confirmation callback
export type DeliveryCallback = (result: PublishResult, payload: EventPayload) => void
