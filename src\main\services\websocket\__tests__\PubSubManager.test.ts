/**
 * PubSubManager Tests
 * Comprehensive test suite for the publish-subscribe system
 */

import { PubSubManager } from '../PubSubManager'
import { EventType, EventChannel, SubscriptionPriority } from '../types/pubsub'

// Mock window for testing
const mockWindow = {
  isDestroyed: jest.fn(() => false),
  webContents: {
    send: jest.fn()
  }
}

// Get the mocked BrowserWindow from the global mock
const { BrowserWindow } = require('electron')

describe('PubSubManager', () => {
  let pubSubManager: PubSubManager
  let clientId: string

  beforeEach(() => {
    // Reset singleton instance for each test
    ;(PubSubManager as any).instance = undefined
    pubSubManager = PubSubManager.getInstance()

    // Mock successful window lookup
    BrowserWindow.fromId.mockReturnValue(mockWindow)
    mockWindow.isDestroyed.mockReturnValue(false)
    mockWindow.webContents.send.mockClear()

    // Register a test client
    clientId = pubSubManager.registerClient(1, { test: true })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('Client Registration', () => {
    it('should register a new client successfully', () => {
      const newClientId = pubSubManager.registerClient(2, { type: 'test' })

      expect(newClientId).toBeDefined()
      expect(newClientId).toMatch(/^client_2_\d+$/)

      const clientInfo = pubSubManager.getClientInfo(newClientId)
      expect(clientInfo).toBeDefined()
      expect(clientInfo?.windowId).toBe(2)
      expect(clientInfo?.metadata).toEqual({ type: 'test' })
      expect(clientInfo?.isActive).toBe(true)
    })

    it('should throw error when max clients exceeded', () => {
      // Update config to limit clients
      pubSubManager.updateConfig({ maxClients: 1 })

      expect(() => {
        pubSubManager.registerClient(3)
      }).toThrow('Maximum clients limit (1) exceeded')
    })

    it('should track active clients correctly', () => {
      const client2 = pubSubManager.registerClient(2)
      const client3 = pubSubManager.registerClient(3)

      const activeClients = pubSubManager.getActiveClients()
      expect(activeClients).toHaveLength(3) // Including the beforeEach client

      pubSubManager.removeClient(client2)
      const updatedActiveClients = pubSubManager.getActiveClients()
      expect(updatedActiveClients).toHaveLength(2)
    })
  })

  describe('Subscription Management', () => {
    it('should create subscription successfully', () => {
      const result = pubSubManager.subscribe({
        clientId,
        eventType: EventType.BALANCE_UPDATE,
        channel: EventChannel.ACCOUNT
      })

      expect(result.success).toBe(true)
      expect(result.subscriptionId).toBeDefined()
      expect(result.message).toBe('Subscription created successfully')

      const subscriptions = pubSubManager.getClientSubscriptions(clientId)
      expect(subscriptions).toHaveLength(1)
      expect(subscriptions[0].eventType).toBe(EventType.BALANCE_UPDATE)
      expect(subscriptions[0].channel).toBe(EventChannel.ACCOUNT)
    })

    it('should fail subscription for non-existent client', () => {
      const result = pubSubManager.subscribe({
        clientId: 'non-existent',
        eventType: EventType.BALANCE_UPDATE
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Client non-existent not found')
    })

    it('should enforce max subscriptions per client', () => {
      pubSubManager.updateConfig({ maxSubscriptionsPerClient: 2 })

      // Create 2 subscriptions (should succeed)
      const sub1 = pubSubManager.subscribe({
        clientId,
        eventType: EventType.BALANCE_UPDATE
      })
      const sub2 = pubSubManager.subscribe({
        clientId,
        eventType: EventType.CHARTS_UPDATE
      })

      expect(sub1.success).toBe(true)
      expect(sub2.success).toBe(true)

      // Third subscription should fail
      const sub3 = pubSubManager.subscribe({
        clientId,
        eventType: EventType.ASSETS_UPDATE
      })

      expect(sub3.success).toBe(false)
      expect(sub3.error).toContain('maximum subscriptions limit')
    })

    it('should unsubscribe successfully', () => {
      const subResult = pubSubManager.subscribe({
        clientId,
        eventType: EventType.BALANCE_UPDATE
      })

      const unsubResult = pubSubManager.unsubscribe(clientId, subResult.subscriptionId!)

      expect(unsubResult.success).toBe(true)
      expect(unsubResult.removedSubscriptions).toBe(1)

      const subscriptions = pubSubManager.getClientSubscriptions(clientId)
      expect(subscriptions).toHaveLength(0)
    })

    it('should unsubscribe all client subscriptions', () => {
      // Create multiple subscriptions
      pubSubManager.subscribe({ clientId, eventType: EventType.BALANCE_UPDATE })
      pubSubManager.subscribe({ clientId, eventType: EventType.CHARTS_UPDATE })
      pubSubManager.subscribe({ clientId, eventType: EventType.ASSETS_UPDATE })

      const result = pubSubManager.unsubscribeAll(clientId)

      expect(result.success).toBe(true)
      expect(result.removedSubscriptions).toBe(3)

      const subscriptions = pubSubManager.getClientSubscriptions(clientId)
      expect(subscriptions).toHaveLength(0)
    })
  })

  describe('Event Publishing', () => {
    beforeEach(() => {
      // Subscribe to events for testing
      pubSubManager.subscribe({
        clientId,
        eventType: EventType.BALANCE_UPDATE,
        channel: EventChannel.ACCOUNT
      })
    })

    it('should publish event to subscribed clients', () => {
      const testData = { balance: 1000, currency: 'USD' }
      const testMetadata = { source: 'test', timestamp: Date.now() }

      const result = pubSubManager.publish(
        EventType.BALANCE_UPDATE,
        testData,
        EventChannel.ACCOUNT,
        testMetadata
      )

      expect(result.success).toBe(true)
      expect(result.deliveredTo).toContain(clientId)
      expect(result.totalSubscribers).toBe(1)
      expect(result.failedDeliveries).toHaveLength(0)

      expect(mockWindow.webContents.send).toHaveBeenCalledWith(
        'ws:event',
        EventType.BALANCE_UPDATE,
        testData,
        expect.objectContaining({ source: 'test', timestamp: expect.any(Number) })
      )
    })

    it('should not deliver to unsubscribed clients', () => {
      const client2 = pubSubManager.registerClient(2)
      // client2 is not subscribed to BALANCE_UPDATE

      const result = pubSubManager.publish(EventType.BALANCE_UPDATE, { balance: 1000 })

      expect(result.deliveredTo).toContain(clientId)
      expect(result.deliveredTo).not.toContain(client2)
      expect(result.totalSubscribers).toBe(1)
    })

    it('should handle destroyed windows gracefully', () => {
      mockWindow.isDestroyed.mockReturnValue(true)

      const result = pubSubManager.publish(EventType.BALANCE_UPDATE, { balance: 1000 })

      expect(result.failedDeliveries).toContain(clientId)
      expect(result.deliveredTo).toHaveLength(0)
    })

    it('should respect subscription priorities', () => {
      const client2 = pubSubManager.registerClient(2)

      // Subscribe with different priorities
      pubSubManager.subscribe({
        clientId,
        eventType: EventType.ERROR,
        priority: SubscriptionPriority.NORMAL
      })

      pubSubManager.subscribe({
        clientId: client2,
        eventType: EventType.ERROR,
        priority: SubscriptionPriority.CRITICAL
      })

      const result = pubSubManager.publish(EventType.ERROR, { message: 'Test error' })

      expect(result.success).toBe(true)
      expect(result.deliveredTo).toEqual([client2, clientId]) // Higher priority first
    })
  })

  describe('Event Filtering', () => {
    it('should filter events by data properties', () => {
      pubSubManager.subscribe({
        clientId,
        eventType: EventType.CHARTS_UPDATE,
        filter: {
          dataFilter: { symbol: 'EURUSD' }
        }
      })

      // Should match filter
      const result1 = pubSubManager.publish(EventType.CHARTS_UPDATE, {
        symbol: 'EURUSD',
        price: 1.1234
      })
      expect(result1.deliveredTo).toContain(clientId)

      // Should not match filter
      const result2 = pubSubManager.publish(EventType.CHARTS_UPDATE, {
        symbol: 'GBPUSD',
        price: 1.2345
      })
      expect(result2.deliveredTo).not.toContain(clientId)
    })

    it('should use custom filter functions', () => {
      pubSubManager.subscribe({
        clientId,
        eventType: EventType.BALANCE_UPDATE,
        filter: {
          customFilter: (payload) => {
            const data = payload.data as any
            return data.balance > 500
          }
        }
      })

      // Should match custom filter
      const result1 = pubSubManager.publish(EventType.BALANCE_UPDATE, { balance: 1000 })
      expect(result1.deliveredTo).toContain(clientId)

      // Should not match custom filter
      const result2 = pubSubManager.publish(EventType.BALANCE_UPDATE, { balance: 100 })
      expect(result2.deliveredTo).not.toContain(clientId)
    })
  })

  describe('Channel Subscriptions', () => {
    it('should deliver events to channel subscribers', () => {
      pubSubManager.subscribe({
        clientId,
        channel: EventChannel.TRADING
      })

      const result = pubSubManager.publish(
        EventType.CHARTS_UPDATE,
        { data: 'test' },
        EventChannel.TRADING
      )

      expect(result.deliveredTo).toContain(clientId)
    })

    it('should deliver all events to ALL channel subscribers', () => {
      pubSubManager.subscribe({
        clientId,
        channel: EventChannel.ALL
      })

      // Test different event types
      const result1 = pubSubManager.publish(EventType.BALANCE_UPDATE, {})
      const result2 = pubSubManager.publish(EventType.CHARTS_UPDATE, {})
      const result3 = pubSubManager.publish(EventType.ERROR, {})

      expect(result1.deliveredTo).toContain(clientId)
      expect(result2.deliveredTo).toContain(clientId)
      expect(result3.deliveredTo).toContain(clientId)
    })
  })

  describe('Statistics and Monitoring', () => {
    it('should track event statistics', () => {
      pubSubManager.subscribe({ clientId, eventType: EventType.BALANCE_UPDATE })

      pubSubManager.publish(EventType.BALANCE_UPDATE, {})
      pubSubManager.publish(EventType.BALANCE_UPDATE, {})
      pubSubManager.publish(EventType.CHARTS_UPDATE, {})

      const stats = pubSubManager.getStats()

      expect(stats.totalEvents).toBe(3)
      expect(stats.eventsByType[EventType.BALANCE_UPDATE]).toBe(2)
      expect(stats.eventsByType[EventType.CHARTS_UPDATE]).toBe(1)
      expect(stats.activeSubscriptions).toBe(1)
      expect(stats.activeClients).toBe(1)
    })

    it('should calculate average delivery time', () => {
      pubSubManager.subscribe({ clientId, eventType: EventType.BALANCE_UPDATE })

      pubSubManager.publish(EventType.BALANCE_UPDATE, {})

      const stats = pubSubManager.getStats()
      expect(stats.averageDeliveryTime).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Memory Management', () => {
    it('should remove client and all subscriptions', () => {
      pubSubManager.subscribe({ clientId, eventType: EventType.BALANCE_UPDATE })
      pubSubManager.subscribe({ clientId, eventType: EventType.CHARTS_UPDATE })

      expect(pubSubManager.getClientSubscriptions(clientId)).toHaveLength(2)

      pubSubManager.removeClient(clientId)

      expect(pubSubManager.getClientInfo(clientId)).toBeUndefined()
      expect(pubSubManager.getClientSubscriptions(clientId)).toHaveLength(0)
    })

    it('should handle cleanup of inactive clients', () => {
      // Set very short timeout for testing
      pubSubManager.updateConfig({ clientTimeoutMs: 100 })

      const client2 = pubSubManager.registerClient(2)

      // Manually trigger cleanup by calling the private method
      // In a real scenario, this would be handled by the timer
      const initialClients = pubSubManager.getActiveClients().length

      // Mark clients as inactive by setting old lastActivity
      const clientInfo = pubSubManager.getClientInfo(client2)
      if (clientInfo) {
        clientInfo.lastActivity = Date.now() - 200 // Older than timeout
      }

      // The cleanup would normally happen automatically
      expect(initialClients).toBeGreaterThan(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle publish errors gracefully', () => {
      mockWindow.webContents.send.mockImplementation(() => {
        throw new Error('Send failed')
      })

      pubSubManager.subscribe({ clientId, eventType: EventType.BALANCE_UPDATE })

      const result = pubSubManager.publish(EventType.BALANCE_UPDATE, {})

      expect(result.success).toBe(true) // Overall operation succeeds
      expect(result.failedDeliveries).toContain(clientId)
      expect(result.deliveredTo).toHaveLength(0)
    })

    it('should validate subscription parameters', () => {
      const result = pubSubManager.subscribe({
        clientId: 'invalid-client',
        eventType: EventType.BALANCE_UPDATE
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('not found')
    })
  })
})
