/**
 * Event Handlers for WebSocket Events
 * Handles incoming socket events and publishes them through the pub-sub system
 */

import Logger from '../../../shared/utils/Logger'
import { PubSubManager } from './PubSubManager'
import { EventType, EventChannel } from './types/pubsub'
import { formatData } from '../../../shared/utils/formatter'

const logger = new Logger({
  prefix: 'EventHandlers',
  enableColors: true
})

export class EventHandlers {
  private pubSub: PubSubManager

  constructor() {
    this.pubSub = PubSubManager.getInstance()
  }

  /**
   * Handle successful authentication
   */
  public handleSuccessAuth = (data?: unknown): void => {
    logger.success('Authentication successful')
    
    this.pubSub.publish(
      EventType.AUTH_SUCCESS,
      data,
      EventChannel.AUTHENTICATION,
      { timestamp: Date.now() }
    )
  }

  /**
   * Handle authentication failure
   */
  public handleAuthFailed = (data?: unknown): void => {
    logger.error('Authentication failed', { data })
    
    this.pubSub.publish(
      EventType.AUTH_FAILED,
      data,
      EventChannel.AUTHENTICATION,
      { timestamp: Date.now() }
    )
  }

  /**
   * Handle balance updates
   */
  public handleBalance = (data: unknown): void => {
    try {
      const formattedData = formatData(data) as AccountBalanceData
      
      logger.info('Balance updated', { balance: formattedData.balance })
      
      this.pubSub.publish(
        EventType.BALANCE_UPDATE,
        formattedData,
        EventChannel.ACCOUNT,
        { 
          timestamp: Date.now(),
          previousBalance: this.getPreviousBalance(),
          balanceChange: this.calculateBalanceChange(formattedData.balance)
        }
      )
    } catch (error) {
      logger.error('Failed to handle balance update', { error: error.message, data })
    }
  }

  /**
   * Handle chart updates
   */
  public handleUpdateCharts = (data: unknown): void => {
    try {
      const formattedData = formatData(data)
      
      logger.debug('Charts updated')
      
      this.pubSub.publish(
        EventType.CHARTS_UPDATE,
        formattedData,
        EventChannel.TRADING,
        { 
          timestamp: Date.now(),
          dataSize: this.getDataSize(formattedData)
        }
      )
    } catch (error) {
      logger.error('Failed to handle chart update', { error: error.message })
    }
  }

  /**
   * Handle history updates (new fast)
   */
  public handleUpdateHistoryNewFast = (data: unknown): void => {
    try {
      const formattedData = formatData(data)
      
      logger.debug('History updated (new fast)')
      
      this.pubSub.publish(
        EventType.HISTORY_UPDATE,
        formattedData,
        EventChannel.TRADING,
        { 
          timestamp: Date.now(),
          updateType: 'new_fast',
          recordCount: this.getRecordCount(formattedData)
        }
      )
    } catch (error) {
      logger.error('Failed to handle history update', { error: error.message })
    }
  }

  /**
   * Handle history loading (period fast)
   */
  public handleLoadHistoryPeriodFast = (data: unknown): void => {
    try {
      const formattedData = formatData(data)
      
      logger.debug('History loaded (period fast)')
      
      this.pubSub.publish(
        EventType.HISTORY_LOAD,
        formattedData,
        EventChannel.TRADING,
        { 
          timestamp: Date.now(),
          loadType: 'period_fast',
          recordCount: this.getRecordCount(formattedData)
        }
      )
    } catch (error) {
      logger.error('Failed to handle history load', { error: error.message })
    }
  }

  /**
   * Handle stream updates
   */
  public handleUpdateStream = (data: unknown): void => {
    try {
      const formattedData = formatData(data)
      
      logger.debug('Stream updated')
      
      this.pubSub.publish(
        EventType.STREAM_UPDATE,
        formattedData,
        EventChannel.TRADING,
        { 
          timestamp: Date.now(),
          streamType: this.getStreamType(formattedData)
        }
      )
    } catch (error) {
      logger.error('Failed to handle stream update', { error: error.message })
    }
  }

  /**
   * Handle asset updates
   */
  public handleUpdateAssets = (data: unknown): void => {
    try {
      const formattedData = formatData(data)
      
      logger.debug('Assets updated')
      
      this.pubSub.publish(
        EventType.ASSETS_UPDATE,
        formattedData,
        EventChannel.TRADING,
        { 
          timestamp: Date.now(),
          assetCount: this.getAssetCount(formattedData)
        }
      )
    } catch (error) {
      logger.error('Failed to handle assets update', { error: error.message })
    }
  }

  /**
   * Handle connection status changes
   */
  public handleConnectionStatus = (status: string, data?: unknown): void => {
    logger.info(`Connection status changed: ${status}`)
    
    this.pubSub.publish(
      EventType.CONNECTION_STATUS,
      { status, ...formatData(data) },
      EventChannel.SYSTEM,
      { timestamp: Date.now() }
    )
  }

  /**
   * Handle heartbeat events
   */
  public handleHeartbeat = (data?: unknown): void => {
    logger.trace('Heartbeat received')
    
    this.pubSub.publish(
      EventType.HEARTBEAT,
      data,
      EventChannel.SYSTEM,
      { timestamp: Date.now() }
    )
  }

  /**
   * Handle error events
   */
  public handleError = (error: Error | string, data?: unknown): void => {
    const errorMessage = typeof error === 'string' ? error : error.message
    
    logger.error('WebSocket error occurred', { error: errorMessage, data })
    
    this.pubSub.publish(
      EventType.ERROR,
      { error: errorMessage, data },
      EventChannel.SYSTEM,
      { timestamp: Date.now() }
    )
  }

  /**
   * Handle custom events
   */
  public handleCustomEvent = (eventName: string, data: unknown): void => {
    logger.info(`Custom event received: ${eventName}`)
    
    this.pubSub.publish(
      EventType.CUSTOM,
      { eventName, data: formatData(data) },
      EventChannel.SYSTEM,
      { 
        timestamp: Date.now(),
        customEventName: eventName
      }
    )
  }

  // Private helper methods
  private previousBalance: number = 0

  private getPreviousBalance(): number {
    return this.previousBalance
  }

  private calculateBalanceChange(newBalance: number): number {
    const change = newBalance - this.previousBalance
    this.previousBalance = newBalance
    return change
  }

  private getDataSize(data: unknown): number {
    if (!data) return 0
    if (Array.isArray(data)) return data.length
    if (typeof data === 'object') return Object.keys(data).length
    return 1
  }

  private getRecordCount(data: unknown): number {
    if (!data) return 0
    if (Array.isArray(data)) return data.length
    if (typeof data === 'object' && data !== null) {
      const obj = data as Record<string, unknown>
      if (obj.records && Array.isArray(obj.records)) return obj.records.length
      if (obj.data && Array.isArray(obj.data)) return obj.data.length
    }
    return 1
  }

  private getStreamType(data: unknown): string {
    if (typeof data === 'object' && data !== null) {
      const obj = data as Record<string, unknown>
      if (obj.type && typeof obj.type === 'string') return obj.type
      if (obj.streamType && typeof obj.streamType === 'string') return obj.streamType
    }
    return 'unknown'
  }

  private getAssetCount(data: unknown): number {
    if (!data) return 0
    if (Array.isArray(data)) return data.length
    if (typeof data === 'object' && data !== null) {
      const obj = data as Record<string, unknown>
      if (obj.assets && Array.isArray(obj.assets)) return obj.assets.length
      if (obj.symbols && Array.isArray(obj.symbols)) return obj.symbols.length
    }
    return 1
  }
}

export default EventHandlers
