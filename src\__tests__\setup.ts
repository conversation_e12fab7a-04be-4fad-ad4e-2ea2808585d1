/**
 * Jest Test Setup
 * Global test configuration and mocks
 */

// Mock Electron modules
const mockBrowserWindow = {
  getAllWindows: jest.fn(() => []),
  fromId: jest.fn(),
  fromWebContents: jest.fn()
}

jest.mock('electron', () => ({
  BrowserWindow: mockBrowserWindow,
  ipcMain: {
    handle: jest.fn(),
    on: jest.fn(),
    removeHandler: jest.fn(),
    removeAllListeners: jest.fn()
  },
  app: {
    on: jest.fn(),
    quit: jest.fn(),
    whenReady: jest.fn(() => Promise.resolve())
  }
}))

// Export mocks for use in tests
;(global as any).mockBrowserWindow = mockBrowserWindow

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => `test-uuid-${Math.random().toString(36).substr(2, 9)}`
  }
})

// Mock console methods to reduce test noise
const originalConsole = { ...console }
beforeEach(() => {
  console.log = jest.fn()
  console.info = jest.fn()
  console.warn = jest.fn()
  console.error = jest.fn()
})

afterEach(() => {
  Object.assign(console, originalConsole)
  jest.clearAllMocks()
})

// Global test timeout
jest.setTimeout(10000)
