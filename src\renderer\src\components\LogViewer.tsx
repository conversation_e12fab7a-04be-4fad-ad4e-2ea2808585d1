import { useEffect, useRef, useState } from 'react'

const MAX_LOGS = 100

export default function LogViewer(): React.JSX.Element {
  const [logs, setLogs] = useState<string[]>([])
  const logEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const unsub = window.api.on('event:log', (data: unknown) => {
      if (typeof data === 'string') {
        setLogs((prevLogs) => {
          const newLogs = [...prevLogs, data]
          logEndRef.current?.scrollIntoView({ behavior: 'smooth' })
          return newLogs.length > MAX_LOGS ? newLogs.slice(-MAX_LOGS) : newLogs
        })
      }
    })

    return () => {
      unsub()
    }
  }, [])

  return (
    <div className="w-full bg-[#181c20] text-[#e0e0e0] font-mono text-[14px] p-2 rounded h-[310px] overflow-y-auto border border-[#333] mt-4 whitespace-pre-wrap">
      {logs.map((log, idx) => (
        <div key={idx}>{log}</div>
      ))}
      <div ref={logEndRef} />
    </div>
  )
}
