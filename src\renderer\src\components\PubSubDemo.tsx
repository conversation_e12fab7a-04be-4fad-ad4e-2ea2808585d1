/**
 * PubSub Demo Component
 * Demonstrates the new publish-subscribe system functionality
 */

import React, { useState, useEffect, useCallback } from 'react'

interface EventLog {
  id: string
  timestamp: number
  eventType: string
  data: unknown
  metadata?: Record<string, unknown>
}

interface SubscriptionInfo {
  id: string
  eventType: string
  channel: string
  active: boolean
}

export default function PubSubDemo(): React.JSX.Element {
  const [eventLogs, setEventLogs] = useState<EventLog[]>([])
  const [subscriptions, setSubscriptions] = useState<SubscriptionInfo[]>([])
  const [clientId, setClientId] = useState<string>('')
  const [isConnected, setIsConnected] = useState(false)
  const [selectedEventType, setSelectedEventType] = useState('account:balance')
  const [selectedChannel, setSelectedChannel] = useState('account')

  // Available event types for demo
  const eventTypes = [
    'auth:success',
    'auth:failed',
    'account:balance',
    'account:status',
    'trading:charts',
    'trading:history',
    'trading:stream',
    'trading:assets',
    'system:connection',
    'system:heartbeat',
    'system:error'
  ]

  const channels = ['auth', 'account', 'trading', 'system', '*']

  // Initialize client and setup event listeners
  useEffect(() => {
    const initializeClient = async (): Promise<void> => {
      try {
        // Register this window as a client
        const id = await window.api.invoke('pubsub:register-client', {
          windowType: 'demo',
          component: 'PubSubDemo'
        })
        setClientId(id)
        setIsConnected(true)
      } catch (error) {
        console.error('Failed to register client:', error)
      }
    }

    initializeClient()

    // Listen for WebSocket events
    const unsubscribe = window.api.on(
      'ws:event',
      (eventType: string, data: unknown, metadata?: Record<string, unknown>) => {
        const logEntry: EventLog = {
          id: `${Date.now()}-${Math.random()}`,
          timestamp: Date.now(),
          eventType,
          data,
          metadata
        }

        setEventLogs((prev) => {
          const newLogs = [logEntry, ...prev].slice(0, 100) // Keep last 100 events
          return newLogs
        })
      }
    )

    return () => {
      unsubscribe()
      if (clientId) {
        window.api.invoke('pubsub:unsubscribe-all', clientId).catch(console.error)
      }
    }
  }, [clientId])

  // Subscribe to an event type
  const handleSubscribe = useCallback(async () => {
    if (!clientId) return

    try {
      const result = await window.api.invoke('pubsub:subscribe', {
        clientId,
        eventType: selectedEventType,
        channel: selectedChannel
      })

      if (result.success) {
        const newSubscription: SubscriptionInfo = {
          id: result.subscriptionId,
          eventType: selectedEventType,
          channel: selectedChannel,
          active: true
        }
        setSubscriptions((prev) => [...prev, newSubscription])
      } else {
        console.error('Subscription failed:', result.error)
      }
    } catch (error) {
      console.error('Failed to subscribe:', error)
    }
  }, [clientId, selectedEventType, selectedChannel])

  // Unsubscribe from an event
  const handleUnsubscribe = useCallback(
    async (subscriptionId: string) => {
      if (!clientId) return

      try {
        const result = await window.api.invoke('pubsub:unsubscribe', clientId, subscriptionId)

        if (result.success) {
          setSubscriptions((prev) =>
            prev.map((sub) => (sub.id === subscriptionId ? { ...sub, active: false } : sub))
          )
        }
      } catch (error) {
        console.error('Failed to unsubscribe:', error)
      }
    },
    [clientId]
  )

  // Publish a test event
  const handlePublishTest = useCallback(async () => {
    try {
      const testData = {
        balance: Math.floor(Math.random() * 10000),
        currency: 'USD',
        timestamp: Date.now()
      }

      await window.api.invoke('pubsub:publish', 'account:balance', testData, 'account', {
        source: 'demo',
        test: true
      })
    } catch (error) {
      console.error('Failed to publish test event:', error)
    }
  }, [])

  // Get system statistics
  const [stats, setStats] = useState<{
    totalEvents: number
    activeClients: number
    activeSubscriptions: number
    averageDeliveryTime: number
  } | null>(null)
  const handleGetStats = useCallback(async () => {
    try {
      const systemStats = await window.api.invoke('pubsub:get-stats')
      setStats(systemStats)
    } catch (error) {
      console.error('Failed to get stats:', error)
    }
  }, [])

  // Clear event logs
  const handleClearLogs = useCallback(() => {
    setEventLogs([])
  }, [])

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6 text-white">WebSocket Pub-Sub Demo</h1>

      {/* Connection Status */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-xl font-semibold mb-2 text-white">Connection Status</h2>
        <div className="flex items-center gap-4">
          <span
            className={`px-3 py-1 rounded-full text-sm ${
              isConnected ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
            }`}
          >
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
          {clientId && <span className="text-gray-300 text-sm">Client ID: {clientId}</span>}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subscription Management */}
        <div className="bg-gray-800 rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-4 text-white">Subscription Management</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Event Type</label>
              <select
                value={selectedEventType}
                onChange={(e) => setSelectedEventType(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              >
                {eventTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Channel</label>
              <select
                value={selectedChannel}
                onChange={(e) => setSelectedChannel(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              >
                {channels.map((channel) => (
                  <option key={channel} value={channel}>
                    {channel}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={handleSubscribe}
              disabled={!isConnected}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white py-2 px-4 rounded"
            >
              Subscribe
            </button>
          </div>

          {/* Active Subscriptions */}
          <div className="mt-6">
            <h3 className="text-lg font-medium text-white mb-2">Active Subscriptions</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {subscriptions
                .filter((sub) => sub.active)
                .map((sub) => (
                  <div
                    key={sub.id}
                    className="flex items-center justify-between bg-gray-700 p-2 rounded"
                  >
                    <span className="text-sm text-gray-300">
                      {sub.eventType} ({sub.channel})
                    </span>
                    <button
                      onClick={() => handleUnsubscribe(sub.id)}
                      className="text-red-400 hover:text-red-300 text-sm"
                    >
                      Unsubscribe
                    </button>
                  </div>
                ))}
              {subscriptions.filter((sub) => sub.active).length === 0 && (
                <p className="text-gray-500 text-sm">No active subscriptions</p>
              )}
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-gray-800 rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-4 text-white">Controls</h2>

          <div className="space-y-3">
            <button
              onClick={handlePublishTest}
              disabled={!isConnected}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white py-2 px-4 rounded"
            >
              Publish Test Event
            </button>

            <button
              onClick={handleGetStats}
              disabled={!isConnected}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white py-2 px-4 rounded"
            >
              Get System Stats
            </button>

            <button
              onClick={handleClearLogs}
              className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded"
            >
              Clear Event Logs
            </button>
          </div>

          {/* System Statistics */}
          {stats && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-white mb-2">System Statistics</h3>
              <div className="bg-gray-700 p-3 rounded text-sm">
                <div className="grid grid-cols-2 gap-2 text-gray-300">
                  <div>Total Events: {stats.totalEvents}</div>
                  <div>Active Clients: {stats.activeClients}</div>
                  <div>Active Subscriptions: {stats.activeSubscriptions}</div>
                  <div>Avg Delivery: {stats.averageDeliveryTime.toFixed(2)}ms</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Event Logs */}
      <div className="mt-6 bg-gray-800 rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4 text-white">Event Logs</h2>
        <div className="bg-gray-900 rounded p-4 h-80 overflow-y-auto">
          {eventLogs.length === 0 ? (
            <p className="text-gray-500">
              No events received yet. Subscribe to events to see them here.
            </p>
          ) : (
            <div className="space-y-2">
              {eventLogs.map((log) => (
                <div key={log.id} className="border-b border-gray-700 pb-2">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400 font-medium">{log.eventType}</span>
                    <span className="text-gray-500 text-xs">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-gray-300 text-sm mt-1">
                    <pre className="whitespace-pre-wrap">{JSON.stringify(log.data, null, 2)}</pre>
                  </div>
                  {log.metadata && (
                    <div className="text-gray-500 text-xs mt-1">
                      Metadata: {JSON.stringify(log.metadata)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
