# Logger Utility Documentation

## Overview

The Logger utility is a comprehensive logging system designed for the Kwartani trading platform. It provides structured logging with multiple levels, file output, color-coded console output, and specialized trading-specific logging methods.

## Features

- **Multiple Log Levels**: TRACE, DEBUG, INFO, SUCCESS, WARN, ERROR
- **Singleton Pattern**: Ensures consistent logging across the application
- **File Logging**: Automatic file rotation and size management
- **Color-coded Output**: Enhanced readability with picocolors
- **Trading-specific Methods**: Specialized logging for trading operations
- **Security Features**: Automatic masking of sensitive data
- **Electron Integration**: Send logs to renderer processes
- **Configurable Settings**: Flexible configuration options

## Installation & Setup

```typescript
import Logger from '@/shared/utils/Logger'

// Get singleton instance with default settings
const logger = Logger.getInstance()

// Or configure with custom settings
const logger = Logger.getInstance({
  showLogLevel: true,
  showTimestamp: true,
  prefix: 'TRADING-APP',
  minLevel: LoggerLevel.DEBUG,
  outputFile: 'app.log',
  maxFileSize: 50, // MB
  enableColors: true,
  dateFormat: 'ISO'
})
```

## Configuration Options

### LoggerSettings Interface

```typescript
interface LoggerSettings {
  showLogLevel: boolean      // Display log level in output
  showTimestamp: boolean     // Display timestamp in output
  prefix?: string           // Custom prefix for all logs
  minLevel?: LoggerLevel    // Minimum level to log
  outputFile?: string       // File name for log output
  maxFileSize?: number      // Max file size in MB before rotation
  enableColors?: boolean    // Enable color-coded output
  dateFormat?: string       // 'ISO' | 'locale' | 'time'
}
```

### Log Levels

```typescript
enum LoggerLevel {
  TRACE = 0,    // Most verbose, for detailed debugging
  DEBUG = 1,    // Debug information
  INFO = 2,     // General information (default)
  SUCCESS = 3,  // Success operations
  WARN = 4,     // Warning messages
  ERROR = 5     // Error messages
}
```

## Basic Usage

### Standard Logging Methods

```typescript
const logger = Logger.getInstance()

// Basic logging
logger.trace('Detailed trace information')
logger.debug('Debug information', { userId: 123 })
logger.info('Application started')
logger.success('Operation completed successfully')
logger.warn('This is a warning')
logger.error('An error occurred', { error: 'Connection failed' })

// With context and data
logger.info('User login', 
  { userId: 123, ip: '***********' }, 
  'AUTH'
)
```

### Trading-Specific Methods

```typescript
// Trading operations
logger.trade('BUY', 'EURUSD', {
  amount: 1000,
  price: 1.0850,
  leverage: 100
})

// Performance monitoring
const startTime = Date.now()
// ... some operation
logger.performance('API_CALL', Date.now() - startTime, {
  endpoint: '/api/trades',
  method: 'POST'
})

// Audit logging
logger.audit('USER_LOGIN', 'user123', {
  ip: '***********',
  userAgent: 'Mozilla/5.0...'
})

// Security events
logger.security('FAILED_LOGIN_ATTEMPT', {
  username: 'admin',
  ip: '***********00',
  attempts: 3
})
```

## Advanced Features

### File Logging

The logger automatically creates a `logs` directory and manages file rotation:

```typescript
const logger = Logger.getInstance({
  outputFile: 'trading.log',
  maxFileSize: 10 // MB
})

// Files are automatically rotated when size limit is reached
// Format: trading-2024-01-15T10-30-00-000Z.log
```

### Log Level Management

```typescript
// Set minimum log level
logger.setLevel(LoggerLevel.WARN) // Only WARN and ERROR will be logged

// Get current level
const currentLevel = logger.getLevel()

// Update settings dynamically
logger.updateSettings({
  minLevel: LoggerLevel.DEBUG,
  enableColors: false
})
```

### Log Buffer Management

```typescript
// Get recent logs
const recentLogs = logger.getLogs(50) // Last 50 entries
const allLogs = logger.getLogs() // All buffered logs

// Clear log buffer
logger.clearLogs()
```

### Electron Integration

```typescript
// Send logs to renderer process
logger.sendToLogView('Custom log message for UI')

// This sends 'event:log' to all browser windows
// Listen in renderer: ipcRenderer.on('event:log', (event, message) => {...})
```

## Output Examples

### Console Output (with colors)

```
2024-01-15T10:30:00.000Z [INFO] [TRADING-APP] [AUTH] User login {"userId":123,"ip":"***********"}
2024-01-15T10:30:01.000Z [SUCCESS] [TRADING-APP] [TRADING] TRADE: BUY {"symbol":"EURUSD","amount":1000}
2024-01-15T10:30:02.000Z [WARN] [TRADING-APP] [SECURITY] SECURITY: FAILED_LOGIN_ATTEMPT {"username":"admin","ip":"***MASKED***"}
```

### File Output

```
2024-01-15T10:30:00.000Z INFO [AUTH] [TRADING-APP] User login {"userId":123,"ip":"***********"}
2024-01-15T10:30:01.000Z SUCCESS [TRADING] [TRADING-APP] TRADE: BUY {"symbol":"EURUSD","amount":1000}
```

## Security Features

### Automatic Data Masking

Sensitive data is automatically masked in security logs:

```typescript
logger.security('API_KEY_USAGE', {
  apiKey: 'secret123',      // Will be masked as ***MASKED***
  password: 'mypass',       // Will be masked as ***MASKED***
  token: 'jwt-token',       // Will be masked as ***MASKED***
  userId: 123               // Will remain visible
})
```

Masked keys include: `password`, `token`, `apiKey`, `secret`, `key`, `auth`, `authorization`

## Best Practices

### 1. Use Appropriate Log Levels

```typescript
// Use TRACE for very detailed debugging
logger.trace('Entering function calculateProfit()', { params })

// Use DEBUG for development debugging
logger.debug('API response received', { responseData })

// Use INFO for general application flow
logger.info('User authenticated successfully')

// Use SUCCESS for completed operations
logger.success('Trade executed', { tradeId, profit })

// Use WARN for recoverable issues
logger.warn('API rate limit approaching', { remaining: 10 })

// Use ERROR for actual errors
logger.error('Database connection failed', { error: err.message })
```

### 2. Include Relevant Context

```typescript
// Good: Provides context and data
logger.trade('SELL', 'GBPUSD', {
  amount: 500,
  price: 1.2650,
  reason: 'stop_loss_triggered',
  originalPrice: 1.2700
})

// Better: Include request ID for tracing
logger.info('Processing trade request', 
  { tradeId: 'TR-123', symbol: 'EURUSD' }, 
  'TRADE_PROCESSOR'
)
```

### 3. Performance Logging

```typescript
// Measure and log performance
const timer = Date.now()
const result = await expensiveOperation()
logger.performance('EXPENSIVE_OPERATION', Date.now() - timer, {
  recordsProcessed: result.length,
  cacheHit: result.fromCache
})
```

### 4. Error Handling

```typescript
try {
  await riskyOperation()
  logger.success('Operation completed successfully')
} catch (error) {
  logger.error('Operation failed', {
    error: error.message,
    stack: error.stack,
    operation: 'riskyOperation'
  })
  throw error
}
```

## Integration Examples

### Express.js Middleware

```typescript
app.use((req, res, next) => {
  const startTime = Date.now()
  
  res.on('finish', () => {
    logger.audit('HTTP_REQUEST', req.user?.id, {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: Date.now() - startTime,
      ip: req.ip
    })
  })
  
  next()
})
```

### Trading Strategy Logging

```typescript
class TradingStrategy {
  private logger = Logger.getInstance({ prefix: 'STRATEGY' })
  
  async executeStrategy(signal: TradingSignal) {
    this.logger.info('Strategy execution started', { signal })
    
    try {
      const trade = await this.placeTrade(signal)
      this.logger.trade('BUY', signal.symbol, {
        strategy: this.name,
        confidence: signal.confidence,
        tradeId: trade.id
      })
      
      this.logger.success('Strategy executed successfully', { tradeId: trade.id })
      return trade
    } catch (error) {
      this.logger.error('Strategy execution failed', {
        error: error.message,
        signal,
        strategy: this.name
      })
      throw error
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Logs not appearing**: Check minimum log level setting
2. **File not created**: Ensure write permissions in the application directory
3. **Colors not showing**: Verify terminal supports colors and `enableColors` is true
4. **Large memory usage**: Reduce log buffer size or increase clear frequency

### Debug Mode

```typescript
// Enable verbose logging for debugging
const logger = Logger.getInstance({
  minLevel: LoggerLevel.TRACE,
  showTimestamp: true,
  showLogLevel: true,
  enableColors: true
})
```

## API Reference

### Constructor Options
- `showLogLevel: boolean` - Show log level in output
- `showTimestamp: boolean` - Show timestamp in output  
- `prefix?: string` - Custom prefix for logs
- `minLevel?: LoggerLevel` - Minimum level to log
- `outputFile?: string` - Output file name
- `maxFileSize?: number` - Max file size in MB
- `enableColors?: boolean` - Enable colored output
- `dateFormat?: string` - Date format ('ISO', 'locale', 'time')

### Methods
- `trace(message, data?, context?)` - Log trace level
- `debug(message, data?, context?)` - Log debug level
- `info(message, data?, context?)` - Log info level
- `success(message, data?, context?)` - Log success level
- `warn(message, data?, context?)` - Log warning level
- `error(message, data?, context?)` - Log error level
- `trade(action, symbol, data?)` - Log trading operations
- `performance(operation, duration, data?)` - Log performance metrics
- `audit(action, userId?, data?)` - Log audit events
- `security(event, data?)` - Log security events
- `setLevel(level)` - Set minimum log level
- `getLevel()` - Get current log level
- `getLogs(count?)` - Get log entries
- `clearLogs()` - Clear log buffer
- `updateSettings(settings)` - Update logger settings
- `sendToLogView(message)` - Send to Electron renderer

This Logger utility provides a robust foundation for application logging with specialized features for trading platforms.
