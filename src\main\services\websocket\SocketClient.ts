import { io, Socket } from 'socket.io-client'
import { AccountType, Auth, SocketStatus, SocketURLs } from './enums'
import Logger from '../../../shared/utils/Logger'
import { EventHandlers } from './EventHandlers'
import { PubSubManager } from './PubSubManager'
import {
  EventType,
  EventChannel,
  ClientId,
  SubscriptionResult,
  UnsubscribeResult,
  PublishResult
} from './types/pubsub'

const logger = new Logger({
  prefix: 'WS',
  enableColors: true
})

class SocketClient {
  private static instance: SocketClient
  private socket: Socket | null = null
  private status: SocketStatus
  private heartbeatInterval: NodeJS.Timeout | null = null
  private pubSub: PubSubManager
  private eventHandlers: EventHandlers

  private constructor() {
    this.status = SocketStatus.DISCONNECTED
    this.pubSub = PubSubManager.getInstance()
    this.eventHandlers = new EventHandlers()
  }

  public static getInstance(): SocketClient {
    if (!SocketClient.instance) {
      SocketClient.instance = new SocketClient()
    }
    return SocketClient.instance
  }

  async connect(): Promise<void> {
    if (this.status === SocketStatus.AUTHENTICATED) return

    logger.info(`Connecting to WebSocket server...`)
    this.setStatus(SocketStatus.CONNECTING)

    return new Promise((resolve, reject) => {
      const options = {
        transports: ['websocket'],
        query: {
          EIO: '4',
          transport: ['websocket']
        }
      }

      this.socket = io(SocketURLs.DEMO, {
        ...options,
        extraHeaders: {
          Origin: SocketURLs.ORIGIN
        },
        path: '/socket.io/'
      })

      this.socket.on('connect', () => {
        if (this.socket) {
          this.socket.emit('auth', {
            isDemo: AccountType.Demo,
            isFastHistory: true,
            platform: 2,
            session: Auth.SESSION_ID,
            uid: Auth.USER_ID
          })

          this.setStatus(SocketStatus.AUTHENTICATING)

          this.setupEventHandlers()
          resolve()
        } else {
          reject()
        }
      })

      this.socket.on('disconnect', () => {
        this.disconnect()
        reject()
      })
    })
  }

  disconnect(): void {
    if (this.socket) {
      this.stopHeartbeat()
      this.socket.disconnect()
      this.socket = null
      this.setStatus(SocketStatus.DISCONNECTED)
    }
  }

  getSocket(): Socket | null {
    return this.socket
  }

  setStatus(status: SocketStatus): void {
    this.status = status
  }

  /**
   * Register a client for pub-sub events
   * @deprecated Use PubSubManager.registerClient() directly
   */
  public registerClient(windowId: number, metadata?: Record<string, unknown>): ClientId {
    return this.pubSub.registerClient(windowId, metadata)
  }

  /**
   * Subscribe to specific events
   * @deprecated Use PubSubManager.subscribe() directly
   */
  public subscribe(
    clientId: ClientId,
    eventType?: EventType,
    channel?: EventChannel
  ): SubscriptionResult {
    return this.pubSub.subscribe({ clientId, eventType, channel })
  }

  /**
   * Unsubscribe from events
   * @deprecated Use PubSubManager.unsubscribe() directly
   */
  public unsubscribe(clientId: ClientId, subscriptionId: string): UnsubscribeResult {
    return this.pubSub.unsubscribe(clientId, subscriptionId)
  }

  /**
   * Remove all subscriptions for a client
   * @deprecated Use PubSubManager.unsubscribeAll() directly
   */
  public unsubscribeAll(clientId: ClientId): UnsubscribeResult {
    return this.pubSub.unsubscribeAll(clientId)
  }

  /**
   * Publish event to subscribers
   * @deprecated Use PubSubManager.publish() directly
   */
  public publish(eventType: EventType, data: unknown, channel?: EventChannel): PublishResult {
    return this.pubSub.publish(eventType, data, channel)
  }

  /**
   * Legacy broadcast method - now uses pub-sub system
   * @deprecated Use publish() method instead
   */
  broadcast(event: string, data: unknown): void {
    // Convert legacy event names to new EventType enum
    const eventType = this.mapLegacyEventToType(event)
    this.pubSub.publish(eventType, data)
  }

  private setupEventHandlers = (): void => {
    if (!this.socket) return

    // Use the new event handlers that integrate with pub-sub
    this.socket.on('successauth', (data) => {
      this.handleSuccessAuth()
      this.eventHandlers.handleSuccessAuth(data)
    })
    this.socket.on('successupdateBalance', this.eventHandlers.handleBalance)
    this.socket.on('updateCharts', this.eventHandlers.handleUpdateCharts)
    this.socket.on('updateHistoryNewFast', this.eventHandlers.handleUpdateHistoryNewFast)
    this.socket.on('loadHistoryPeriodFast', this.eventHandlers.handleLoadHistoryPeriodFast)
    this.socket.on('updateStream', this.eventHandlers.handleUpdateStream)
    this.socket.on('updateAssets', this.eventHandlers.handleUpdateAssets)

    // Handle connection events
    this.socket.on('connect', () => {
      this.eventHandlers.handleConnectionStatus('connected')
    })

    this.socket.on('disconnect', () => {
      this.eventHandlers.handleConnectionStatus('disconnected')
    })

    this.socket.on('error', (error) => {
      this.eventHandlers.handleError(error)
    })

    // Log all events for debugging
    this.socket.onAny((event, ...args) => {
      logger.debug(`Received event: ${event}`, { args })
    })
  }

  /**
   * Handle successful authentication - now integrated with pub-sub
   */
  private handleSuccessAuth = (): void => {
    this.setStatus(SocketStatus.AUTHENTICATED)
    this.startHeartbeat()
    // The EventHandlers will publish the auth success event
  }

  /**
   * Map legacy event names to new EventType enum
   */
  private mapLegacyEventToType(event: string): EventType {
    const eventMap: Record<string, EventType> = {
      successauth: EventType.AUTH_SUCCESS,
      successupdateBalance: EventType.BALANCE_UPDATE,
      updateCharts: EventType.CHARTS_UPDATE,
      updateHistoryNewFast: EventType.HISTORY_UPDATE,
      loadHistoryPeriodFast: EventType.HISTORY_LOAD,
      updateStream: EventType.STREAM_UPDATE,
      updateAssets: EventType.ASSETS_UPDATE,
      connect: EventType.CONNECTION_STATUS,
      disconnect: EventType.CONNECTION_STATUS,
      error: EventType.ERROR
    }

    return eventMap[event] || EventType.CUSTOM
  }

  private startHeartbeat = (): void => {
    if (!this.socket) return

    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.socket.connected) {
        this.socket.emit('ps')
      }
    }, 20000)
  }

  private stopHeartbeat = (): void => {
    if (!this.socket || !this.heartbeatInterval) return

    clearInterval(this.heartbeatInterval)
    this.heartbeatInterval = null
    this.socket.off('ps')
  }
}

export default SocketClient
