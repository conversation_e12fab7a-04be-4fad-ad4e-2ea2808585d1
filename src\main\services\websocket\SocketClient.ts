import { io, Socket } from 'socket.io-client'
import { AccountType, Auth, SocketStatus, SocketURLs } from './enums'
import Logger from '../../../shared/utils/Logger'
import EventHandlers from './EventHandlers'
import { <PERSON><PERSON>erWindow } from 'electron'
import { formatData } from '../../../shared/utils/formatter'

const logger = new Logger({
  prefix: 'WS',
  enableColors: true
})

class SocketClient {
  private static instance: SocketClient
  private socket: Socket | null = null
  private status: SocketStatus
  private accountBalance: number = 0
  private heartbeatInterval: NodeJS.Timeout | null = null

  private constructor() {
    this.status = SocketStatus.DISCONNECTED
  }

  public static getInstance(): SocketClient {
    if (!SocketClient.instance) {
      SocketClient.instance = new SocketClient()
    }
    return SocketClient.instance
  }

  async connect(): Promise<void> {
    if (this.status === SocketStatus.AUTHENTICATED) return

    logger.info(`Connecting to WebSocket server...`)
    this.setStatus(SocketStatus.CONNECTING)

    return new Promise((resolve, reject) => {
      const options = {
        transports: ['websocket'],
        query: {
          EIO: '4',
          transport: ['websocket']
        }
      }

      this.socket = io(SocketURLs.DEMO, {
        ...options,
        extraHeaders: {
          Origin: SocketURLs.ORIGIN
        },
        path: '/socket.io/'
      })

      this.socket.on('connect', () => {
        if (this.socket) {
          this.socket.emit('auth', {
            isDemo: AccountType.Demo,
            isFastHistory: true,
            platform: 2,
            session: Auth.SESSION_ID,
            uid: Auth.USER_ID
          })

          this.setStatus(SocketStatus.AUTHENTICATING)

          this.setupEventHandlers()
          resolve()
        } else {
          reject()
        }
      })

      this.socket.on('disconnect', () => {
        this.disconnect()
        reject()
      })
    })
  }

  disconnect(): void {
    if (this.socket) {
      this.stopHeartbeat()
      this.socket.disconnect()
      this.socket = null
      this.setStatus(SocketStatus.DISCONNECTED)
    }
  }

  getSocket(): Socket | null {
    return this.socket
  }

  setStatus(status: SocketStatus): void {
    this.status = status
  }

  broadcast(event: string, data: unknown): void {
    BrowserWindow.getAllWindows().forEach((window) => {
      if (window && !window.isDestroyed() && window.webContents) {
        window.webContents.send('ws:event', event, formatData(data))
      }
    })
  }

  private setupEventHandlers = (): void => {
    if (!this.socket) return

    this.socket.on('successauth', this.handleSuccessAuth)
    this.socket.on('successupdateBalance', this.handleBalance)
    this.socket.on('updateCharts', this.handleUpdateCharts)
    this.socket.on('updateHistoryNewFast', this.handleUpdateHistoryNewFast)
    this.socket.on('loadHistoryPeriodFast', this.handleLoadHistoryPeriodFast)
    this.socket.on('updateStream', this.handleUpdateStream)
    this.socket.on('updateAssets', this.handleUpdateAssets)

    this.socket.onAny((event) => {
      logger.info(`Received event: ${event}`)
    })
  }

  private handleSuccessAuth = (): void => {
    this.setStatus(SocketStatus.AUTHENTICATED)
    this.startHeartbeat()
  }

  private handleBalance = (data: unknown): void => {
    const formattedData = formatData(data) as AccountBalanceData
    this.accountBalance = formattedData.balance

    // Notify subscibers here
  }

  private handleUpdateCharts = (data: unknown): void => {
    logger.info('Charts updated', data)
  }

  private handleUpdateHistoryNewFast = (data: unknown): void => {
    logger.info('History updated', data)
  }

  private handleLoadHistoryPeriodFast = (data: unknown): void => {
    logger.info('History loaded', data)
  }

  private handleUpdateStream = (data: unknown): void => {
    logger.info('Stream updated', data)
  }

  private handleUpdateAssets = (data: unknown): void => {
    logger.info('Assets updated', data)
  }

  private startHeartbeat = (): void => {
    if (!this.socket) return

    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.socket.connected) {
        this.socket.emit('ps')
      }
    }, 20000)
  }

  private stopHeartbeat = (): void => {
    if (!this.socket || !this.heartbeatInterval) return

    clearInterval(this.heartbeatInterval)
    this.heartbeatInterval = null
    this.socket.off('ps')
  }
}

export default SocketClient
