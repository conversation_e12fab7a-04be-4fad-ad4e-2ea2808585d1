export const formatData = (data: unknown): unknown | null => {
  try {
    if (data instanceof Buffer || data instanceof ArrayBuffer) {
      const buffer = data as Buffer
      const jsonStr = buffer.toString('utf-8')
      return JSON.parse(jsonStr)
    }

    if (typeof data === 'string') {
      try {
        return JSON.parse(data)
      } catch {
        return data
      }
    }

    return data
  } catch {
    return null
  }
}
