import { AccountType } from './enums'
import { EventType, EventChannel, SubscriptionId } from './types/pubsub'

declare global {
  interface AccountBalanceData {
    isDemo: AccountType
    balance: number
  }

  // Global pub-sub types for easier access
  interface Window {
    pubsub?: {
      subscribe: (eventType: EventType, callback: (data: unknown) => void) => SubscriptionId
      unsubscribe: (subscriptionId: SubscriptionId) => void
      publish: (eventType: EventType, data: unknown) => void
    }
  }

  // WebSocket event data types
  interface WSEventData {
    eventType: EventType
    channel: EventChannel
    data: unknown
    metadata?: Record<string, unknown>
    timestamp: number
  }

  // Legacy support for existing code
  interface LegacyWSEvent {
    event: string
    data: unknown
  }
}

export {}
