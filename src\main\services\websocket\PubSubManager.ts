/**
 * Publish-Subscribe Manager
 * Core implementation of the pub-sub pattern for WebSocket event distribution
 */

import { BrowserWindow } from 'electron'
import { randomUUID } from 'crypto'
import Logger from '../../../shared/utils/Logger'
import { formatData } from '../../../shared/utils/formatter'
import {
  EventType,
  EventChannel,
  ClientId,
  SubscriptionId,
  EventPayload,
  SubscriptionConfig,
  Subscription,
  ClientInfo,
  SubscriptionResult,
  PublishResult,
  UnsubscribeResult,
  EventStats,
  PubSubConfig,
  PubSubError,
  PubSubErrorType,
  EventHistoryEntry,
  SubscriptionPriority,
  SubscriptionMatcher,
  EventTransformer,
  DeliveryCallback
} from './types/pubsub'

const logger = new Logger({
  prefix: 'PubSub',
  enableColors: true
})

export class PubSubManager {
  private static instance: PubSubManager
  private subscriptions = new Map<SubscriptionId, Subscription>()
  private clients = new Map<ClientId, ClientInfo>()
  private eventHistory: EventHistoryEntry[] = []
  private stats: EventStats = {
    totalEvents: 0,
    eventsByType: {} as Record<EventType, number>,
    eventsByChannel: {} as Record<EventChannel, number>,
    activeSubscriptions: 0,
    activeClients: 0,
    averageDeliveryTime: 0
  }

  private config: PubSubConfig = {
    maxSubscriptionsPerClient: 50,
    maxClients: 100,
    clientTimeoutMs: 300000, // 5 minutes
    enableEventHistory: true,
    eventHistorySize: 1000,
    enableMetrics: true,
    deliveryTimeoutMs: 5000
  }

  private constructor() {
    this.startCleanupTimer()
  }

  public static getInstance(): PubSubManager {
    if (!PubSubManager.instance) {
      PubSubManager.instance = new PubSubManager()
    }
    return PubSubManager.instance
  }

  /**
   * Register a new client
   */
  public registerClient(windowId: number, metadata?: Record<string, unknown>): ClientId {
    const clientId = `client_${windowId}_${Date.now()}`

    if (this.clients.size >= this.config.maxClients) {
      throw new PubSubError(
        PubSubErrorType.MAX_CLIENTS_EXCEEDED,
        `Maximum clients limit (${this.config.maxClients}) exceeded`
      )
    }

    const clientInfo: ClientInfo = {
      id: clientId,
      windowId,
      subscriptions: new Set(),
      isActive: true,
      connectedAt: Date.now(),
      lastActivity: Date.now(),
      metadata
    }

    this.clients.set(clientId, clientInfo)
    this.updateStats()

    logger.info(`Client registered: ${clientId}`, { windowId, metadata })
    return clientId
  }

  /**
   * Subscribe a client to specific events or channels
   */
  public subscribe(config: SubscriptionConfig): SubscriptionResult {
    try {
      const client = this.clients.get(config.clientId)
      if (!client) {
        throw new PubSubError(
          PubSubErrorType.CLIENT_NOT_FOUND,
          `Client ${config.clientId} not found`
        )
      }

      if (client.subscriptions.size >= this.config.maxSubscriptionsPerClient) {
        throw new PubSubError(
          PubSubErrorType.MAX_SUBSCRIPTIONS_EXCEEDED,
          `Client ${config.clientId} has reached maximum subscriptions limit`
        )
      }

      const subscriptionId = randomUUID()
      const subscription: Subscription = {
        id: subscriptionId,
        clientId: config.clientId,
        eventType: config.eventType,
        channel: config.channel,
        filter: config.filter,
        priority: config.priority || SubscriptionPriority.NORMAL,
        createdAt: Date.now(),
        lastActivity: Date.now()
      }

      this.subscriptions.set(subscriptionId, subscription)
      client.subscriptions.add(subscriptionId)
      client.lastActivity = Date.now()

      this.updateStats()

      logger.info(`Subscription created: ${subscriptionId}`, {
        clientId: config.clientId,
        eventType: config.eventType,
        channel: config.channel
      })

      return {
        success: true,
        subscriptionId,
        message: 'Subscription created successfully'
      }
    } catch (error) {
      logger.error('Subscription failed', { error: error.message, config })
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * Unsubscribe from specific subscription
   */
  public unsubscribe(clientId: ClientId, subscriptionId: SubscriptionId): UnsubscribeResult {
    try {
      const client = this.clients.get(clientId)
      if (!client) {
        throw new PubSubError(PubSubErrorType.CLIENT_NOT_FOUND, `Client ${clientId} not found`)
      }

      const subscription = this.subscriptions.get(subscriptionId)
      if (!subscription || subscription.clientId !== clientId) {
        throw new PubSubError(
          PubSubErrorType.SUBSCRIPTION_NOT_FOUND,
          `Subscription ${subscriptionId} not found for client ${clientId}`
        )
      }

      this.subscriptions.delete(subscriptionId)
      client.subscriptions.delete(subscriptionId)
      client.lastActivity = Date.now()

      this.updateStats()

      logger.info(`Subscription removed: ${subscriptionId}`, { clientId })

      return {
        success: true,
        removedSubscriptions: 1,
        message: 'Subscription removed successfully'
      }
    } catch (error) {
      logger.error('Unsubscribe failed', { error: error.message, clientId, subscriptionId })
      return {
        success: false,
        removedSubscriptions: 0,
        error: error.message
      }
    }
  }

  /**
   * Remove all subscriptions for a client
   */
  public unsubscribeAll(clientId: ClientId): UnsubscribeResult {
    try {
      const client = this.clients.get(clientId)
      if (!client) {
        throw new PubSubError(PubSubErrorType.CLIENT_NOT_FOUND, `Client ${clientId} not found`)
      }

      const subscriptionIds = Array.from(client.subscriptions)
      let removedCount = 0

      for (const subscriptionId of subscriptionIds) {
        if (this.subscriptions.delete(subscriptionId)) {
          removedCount++
        }
      }

      client.subscriptions.clear()
      client.lastActivity = Date.now()

      this.updateStats()

      logger.info(`All subscriptions removed for client: ${clientId}`, { removedCount })

      return {
        success: true,
        removedSubscriptions: removedCount,
        message: `Removed ${removedCount} subscriptions`
      }
    } catch (error) {
      logger.error('Unsubscribe all failed', { error: error.message, clientId })
      return {
        success: false,
        removedSubscriptions: 0,
        error: error.message
      }
    }
  }

  /**
   * Publish event to subscribers
   */
  public publish(
    eventType: EventType,
    data: unknown,
    channel?: EventChannel,
    metadata?: Record<string, unknown>,
    callback?: DeliveryCallback
  ): PublishResult {
    const startTime = Date.now()

    try {
      const payload: EventPayload = {
        timestamp: Date.now(),
        eventType,
        channel: channel || this.getChannelFromEventType(eventType),
        data: formatData(data),
        metadata
      }

      const matchingSubscriptions = this.findMatchingSubscriptions(payload)
      const deliveredTo: ClientId[] = []
      const failedDeliveries: ClientId[] = []

      // Sort by priority (highest first)
      const sortedSubscriptions = matchingSubscriptions.sort((a, b) => b.priority - a.priority)

      for (const subscription of sortedSubscriptions) {
        try {
          const client = this.clients.get(subscription.clientId)
          if (!client || !client.isActive) {
            failedDeliveries.push(subscription.clientId)
            continue
          }

          const window = BrowserWindow.fromId(client.windowId)
          if (!window || window.isDestroyed()) {
            this.markClientInactive(subscription.clientId)
            failedDeliveries.push(subscription.clientId)
            continue
          }

          // Send event to client
          window.webContents.send('ws:event', eventType, payload.data, payload.metadata)

          deliveredTo.push(subscription.clientId)
          subscription.lastActivity = Date.now()
          client.lastActivity = Date.now()
        } catch (error) {
          logger.error(`Failed to deliver event to client ${subscription.clientId}`, {
            error: error.message,
            eventType,
            subscriptionId: subscription.id
          })
          failedDeliveries.push(subscription.clientId)
        }
      }

      // Update statistics
      this.updateEventStats(eventType, payload.channel, Date.now() - startTime)

      // Store in history if enabled
      if (this.config.enableEventHistory) {
        this.addToHistory(payload, deliveredTo, Date.now() - startTime)
      }

      const result: PublishResult = {
        success: true,
        deliveredTo,
        failedDeliveries,
        totalSubscribers: matchingSubscriptions.length
      }

      logger.info(`Event published: ${eventType}`, {
        delivered: deliveredTo.length,
        failed: failedDeliveries.length,
        total: matchingSubscriptions.length,
        deliveryTime: Date.now() - startTime
      })

      if (callback) {
        callback(result, payload)
      }

      return result
    } catch (error) {
      logger.error('Publish failed', { error: error.message, eventType, channel })
      const result: PublishResult = {
        success: false,
        deliveredTo: [],
        failedDeliveries: [],
        totalSubscribers: 0,
        error: error.message
      }

      if (callback) {
        callback(result, {
          timestamp: Date.now(),
          eventType,
          channel: channel || EventChannel.SYSTEM,
          data
        })
      }

      return result
    }
  }

  /**
   * Remove client and all its subscriptions
   */
  public removeClient(clientId: ClientId): void {
    const client = this.clients.get(clientId)
    if (!client) return

    // Remove all subscriptions for this client
    for (const subscriptionId of client.subscriptions) {
      this.subscriptions.delete(subscriptionId)
    }

    this.clients.delete(clientId)
    this.updateStats()

    logger.info(`Client removed: ${clientId}`, {
      subscriptionsRemoved: client.subscriptions.size
    })
  }

  /**
   * Get current statistics
   */
  public getStats(): EventStats {
    return { ...this.stats }
  }

  /**
   * Get client information
   */
  public getClientInfo(clientId: ClientId): ClientInfo | undefined {
    return this.clients.get(clientId)
  }

  /**
   * Get all active clients
   */
  public getActiveClients(): ClientInfo[] {
    return Array.from(this.clients.values()).filter((client) => client.isActive)
  }

  /**
   * Get subscriptions for a client
   */
  public getClientSubscriptions(clientId: ClientId): Subscription[] {
    const client = this.clients.get(clientId)
    if (!client) return []

    return Array.from(client.subscriptions)
      .map((id) => this.subscriptions.get(id))
      .filter(Boolean) as Subscription[]
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<PubSubConfig>): void {
    this.config = { ...this.config, ...newConfig }
    logger.info('PubSub configuration updated', newConfig)
  }

  // Private helper methods
  private findMatchingSubscriptions(payload: EventPayload): Subscription[] {
    const matching: Subscription[] = []

    for (const subscription of this.subscriptions.values()) {
      if (this.isSubscriptionMatch(subscription, payload)) {
        matching.push(subscription)
      }
    }

    return matching
  }

  private isSubscriptionMatch(subscription: Subscription, payload: EventPayload): boolean {
    // Check event type match
    if (subscription.eventType && subscription.eventType !== payload.eventType) {
      return false
    }

    // Check channel match
    if (
      subscription.channel &&
      subscription.channel !== EventChannel.ALL &&
      subscription.channel !== payload.channel
    ) {
      return false
    }

    // Apply custom filter if present
    if (subscription.filter?.customFilter) {
      return subscription.filter.customFilter(payload)
    }

    // Apply data filter if present
    if (subscription.filter?.dataFilter) {
      return this.matchesDataFilter(payload.data, subscription.filter.dataFilter)
    }

    // Apply metadata filter if present
    if (subscription.filter?.metadataFilter && payload.metadata) {
      return this.matchesDataFilter(payload.metadata, subscription.filter.metadataFilter)
    }

    return true
  }

  private matchesDataFilter(data: unknown, filter: Record<string, unknown>): boolean {
    if (!data || typeof data !== 'object') return false

    const dataObj = data as Record<string, unknown>

    for (const [key, value] of Object.entries(filter)) {
      if (dataObj[key] !== value) {
        return false
      }
    }

    return true
  }

  private getChannelFromEventType(eventType: EventType): EventChannel {
    if (eventType.startsWith('auth:')) return EventChannel.AUTHENTICATION
    if (eventType.startsWith('account:')) return EventChannel.ACCOUNT
    if (eventType.startsWith('trading:')) return EventChannel.TRADING
    if (eventType.startsWith('system:')) return EventChannel.SYSTEM
    return EventChannel.SYSTEM
  }

  private markClientInactive(clientId: ClientId): void {
    const client = this.clients.get(clientId)
    if (client) {
      client.isActive = false
      logger.warn(`Client marked inactive: ${clientId}`)
    }
  }

  private updateStats(): void {
    this.stats.activeSubscriptions = this.subscriptions.size
    this.stats.activeClients = Array.from(this.clients.values()).filter(
      (client) => client.isActive
    ).length
  }

  private updateEventStats(
    eventType: EventType,
    channel: EventChannel,
    deliveryTime: number
  ): void {
    this.stats.totalEvents++
    this.stats.eventsByType[eventType] = (this.stats.eventsByType[eventType] || 0) + 1
    this.stats.eventsByChannel[channel] = (this.stats.eventsByChannel[channel] || 0) + 1

    // Update average delivery time
    const totalTime = this.stats.averageDeliveryTime * (this.stats.totalEvents - 1) + deliveryTime
    this.stats.averageDeliveryTime = totalTime / this.stats.totalEvents
  }

  private addToHistory(payload: EventPayload, deliveredTo: ClientId[], deliveryTime: number): void {
    const entry: EventHistoryEntry = {
      id: randomUUID(),
      payload,
      deliveredTo,
      deliveryTime,
      createdAt: Date.now()
    }

    this.eventHistory.push(entry)

    // Trim history if it exceeds max size
    if (this.eventHistory.length > this.config.eventHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.config.eventHistorySize)
    }
  }

  private startCleanupTimer(): void {
    setInterval(() => {
      this.cleanupInactiveClients()
    }, 60000) // Run every minute
  }

  private cleanupInactiveClients(): void {
    const now = Date.now()
    const clientsToRemove: ClientId[] = []

    for (const [clientId, client] of this.clients.entries()) {
      if (!client.isActive || now - client.lastActivity > this.config.clientTimeoutMs) {
        clientsToRemove.push(clientId)
      }
    }

    for (const clientId of clientsToRemove) {
      this.removeClient(clientId)
    }

    if (clientsToRemove.length > 0) {
      logger.info(`Cleaned up ${clientsToRemove.length} inactive clients`)
    }
  }
}
