/**
 * IPC Handlers for Pub-Sub System
 * Provides IPC communication between renderer and main process for pub-sub operations
 */

import { ipc<PERSON>ain, BrowserWindow } from 'electron'
import { PubSubManager } from '../services/websocket/PubSubManager'
import { EventType, EventChannel, SubscriptionConfig } from '../services/websocket/types/pubsub'
import Logger from '../../shared/utils/Logger'

const logger = new Logger({
  prefix: 'PubSubIPC',
  enableColors: true
})

const pubSubManager = PubSubManager.getInstance()

/**
 * Register all pub-sub IPC handlers
 */
export function registerPubSubHandlers(): void {
  logger.info('Registering pub-sub IPC handlers')

  // Register a client
  ipcMain.handle('pubsub:register-client', async (event, metadata?: Record<string, unknown>) => {
    try {
      const window = BrowserWindow.fromWebContents(event.sender)
      if (!window) {
        throw new Error('Could not find window for client registration')
      }

      const clientId = pubSubManager.registerClient(window.id, metadata)
      
      logger.info('Client registered via IPC', { clientId, windowId: window.id, metadata })
      return clientId
    } catch (error) {
      logger.error('Failed to register client via IPC', { error: error.message })
      throw error
    }
  })

  // Subscribe to events
  ipcMain.handle('pubsub:subscribe', async (event, config: SubscriptionConfig) => {
    try {
      const result = pubSubManager.subscribe(config)
      
      logger.info('Subscription created via IPC', { 
        clientId: config.clientId,
        eventType: config.eventType,
        channel: config.channel,
        success: result.success
      })
      
      return result
    } catch (error) {
      logger.error('Failed to subscribe via IPC', { error: error.message, config })
      return {
        success: false,
        error: error.message
      }
    }
  })

  // Unsubscribe from specific subscription
  ipcMain.handle('pubsub:unsubscribe', async (event, clientId: string, subscriptionId: string) => {
    try {
      const result = pubSubManager.unsubscribe(clientId, subscriptionId)
      
      logger.info('Unsubscribed via IPC', { clientId, subscriptionId, success: result.success })
      return result
    } catch (error) {
      logger.error('Failed to unsubscribe via IPC', { error: error.message, clientId, subscriptionId })
      return {
        success: false,
        error: error.message
      }
    }
  })

  // Unsubscribe all for a client
  ipcMain.handle('pubsub:unsubscribe-all', async (event, clientId: string) => {
    try {
      const result = pubSubManager.unsubscribeAll(clientId)
      
      logger.info('Unsubscribed all via IPC', { clientId, success: result.success })
      return result
    } catch (error) {
      logger.error('Failed to unsubscribe all via IPC', { error: error.message, clientId })
      return {
        success: false,
        error: error.message
      }
    }
  })

  // Publish event
  ipcMain.handle('pubsub:publish', async (
    event, 
    eventType: EventType, 
    data: unknown, 
    channel?: EventChannel, 
    metadata?: Record<string, unknown>
  ) => {
    try {
      const result = pubSubManager.publish(eventType, data, channel, metadata)
      
      logger.info('Event published via IPC', { 
        eventType, 
        channel, 
        delivered: result.deliveredTo.length,
        failed: result.failedDeliveries.length
      })
      
      return result
    } catch (error) {
      logger.error('Failed to publish via IPC', { error: error.message, eventType, channel })
      return {
        success: false,
        error: error.message,
        deliveredTo: [],
        failedDeliveries: [],
        totalSubscribers: 0
      }
    }
  })

  // Get client information
  ipcMain.handle('pubsub:get-client-info', async (event, clientId: string) => {
    try {
      const clientInfo = pubSubManager.getClientInfo(clientId)
      return clientInfo
    } catch (error) {
      logger.error('Failed to get client info via IPC', { error: error.message, clientId })
      return null
    }
  })

  // Get active clients
  ipcMain.handle('pubsub:get-active-clients', async (event) => {
    try {
      const activeClients = pubSubManager.getActiveClients()
      return activeClients
    } catch (error) {
      logger.error('Failed to get active clients via IPC', { error: error.message })
      return []
    }
  })

  // Get client subscriptions
  ipcMain.handle('pubsub:get-client-subscriptions', async (event, clientId: string) => {
    try {
      const subscriptions = pubSubManager.getClientSubscriptions(clientId)
      return subscriptions
    } catch (error) {
      logger.error('Failed to get client subscriptions via IPC', { error: error.message, clientId })
      return []
    }
  })

  // Get system statistics
  ipcMain.handle('pubsub:get-stats', async (event) => {
    try {
      const stats = pubSubManager.getStats()
      return stats
    } catch (error) {
      logger.error('Failed to get stats via IPC', { error: error.message })
      return {
        totalEvents: 0,
        eventsByType: {},
        eventsByChannel: {},
        activeSubscriptions: 0,
        activeClients: 0,
        averageDeliveryTime: 0
      }
    }
  })

  // Update configuration
  ipcMain.handle('pubsub:update-config', async (event, newConfig: any) => {
    try {
      pubSubManager.updateConfig(newConfig)
      logger.info('Configuration updated via IPC', newConfig)
      return { success: true }
    } catch (error) {
      logger.error('Failed to update config via IPC', { error: error.message, newConfig })
      return { success: false, error: error.message }
    }
  })

  // Remove client (usually called when window closes)
  ipcMain.handle('pubsub:remove-client', async (event, clientId: string) => {
    try {
      pubSubManager.removeClient(clientId)
      logger.info('Client removed via IPC', { clientId })
      return { success: true }
    } catch (error) {
      logger.error('Failed to remove client via IPC', { error: error.message, clientId })
      return { success: false, error: error.message }
    }
  })

  // Subscribe to channel (convenience method)
  ipcMain.handle('pubsub:subscribe-channel', async (event, clientId: string, channel: EventChannel) => {
    try {
      const result = pubSubManager.subscribe({
        clientId,
        channel
      })
      
      logger.info('Channel subscription created via IPC', { clientId, channel, success: result.success })
      return result
    } catch (error) {
      logger.error('Failed to subscribe to channel via IPC', { error: error.message, clientId, channel })
      return {
        success: false,
        error: error.message
      }
    }
  })

  // Subscribe to event type (convenience method)
  ipcMain.handle('pubsub:subscribe-event', async (event, clientId: string, eventType: EventType) => {
    try {
      const result = pubSubManager.subscribe({
        clientId,
        eventType
      })
      
      logger.info('Event subscription created via IPC', { clientId, eventType, success: result.success })
      return result
    } catch (error) {
      logger.error('Failed to subscribe to event via IPC', { error: error.message, clientId, eventType })
      return {
        success: false,
        error: error.message
      }
    }
  })

  // Bulk subscribe (subscribe to multiple events at once)
  ipcMain.handle('pubsub:bulk-subscribe', async (event, clientId: string, configs: Partial<SubscriptionConfig>[]) => {
    try {
      const results = []
      
      for (const config of configs) {
        const result = pubSubManager.subscribe({
          clientId,
          ...config
        })
        results.push(result)
      }
      
      const successCount = results.filter(r => r.success).length
      
      logger.info('Bulk subscription completed via IPC', { 
        clientId, 
        total: configs.length, 
        successful: successCount 
      })
      
      return {
        success: successCount > 0,
        results,
        successCount,
        totalCount: configs.length
      }
    } catch (error) {
      logger.error('Failed to bulk subscribe via IPC', { error: error.message, clientId })
      return {
        success: false,
        error: error.message,
        results: [],
        successCount: 0,
        totalCount: configs.length
      }
    }
  })

  // Health check
  ipcMain.handle('pubsub:health-check', async (event) => {
    try {
      const stats = pubSubManager.getStats()
      const activeClients = pubSubManager.getActiveClients()
      
      return {
        healthy: true,
        stats,
        clientCount: activeClients.length,
        timestamp: Date.now()
      }
    } catch (error) {
      logger.error('Health check failed via IPC', { error: error.message })
      return {
        healthy: false,
        error: error.message,
        timestamp: Date.now()
      }
    }
  })

  logger.info('Pub-sub IPC handlers registered successfully')
}

/**
 * Unregister all pub-sub IPC handlers
 */
export function unregisterPubSubHandlers(): void {
  const handlers = [
    'pubsub:register-client',
    'pubsub:subscribe',
    'pubsub:unsubscribe',
    'pubsub:unsubscribe-all',
    'pubsub:publish',
    'pubsub:get-client-info',
    'pubsub:get-active-clients',
    'pubsub:get-client-subscriptions',
    'pubsub:get-stats',
    'pubsub:update-config',
    'pubsub:remove-client',
    'pubsub:subscribe-channel',
    'pubsub:subscribe-event',
    'pubsub:bulk-subscribe',
    'pubsub:health-check'
  ]

  handlers.forEach(handler => {
    ipcMain.removeHandler(handler)
  })

  logger.info('Pub-sub IPC handlers unregistered')
}
