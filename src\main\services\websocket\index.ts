/**
 * WebSocket Service Exports
 * Central export file for all WebSocket and pub-sub related functionality
 */

// Core classes
export { default as SocketClient } from './SocketClient'
export { PubSubManager } from './PubSubManager'
export { EventHandlers } from './EventHandlers'

// Enums and constants
export { AccountType, SocketStatus, SocketURLs, Auth } from './enums'

// Types and interfaces
export * from './types/pubsub'

// Utility functions
export { formatData } from '../../../shared/utils/formatter'

/**
 * Convenience function to get the singleton instances
 */
export const getSocketClient = (): SocketClient => SocketClient.getInstance()
export const getPubSubManager = (): PubSubManager => PubSubManager.getInstance()

/**
 * Initialize the WebSocket service with pub-sub system
 */
export const initializeWebSocketService = (): {
  socketClient: SocketClient
  pubSubManager: PubSubManager
  connect: () => Promise<void>
  disconnect: () => void
  registerClient: (windowId: number, metadata?: Record<string, unknown>) => string
  subscribe: (config: SubscriptionConfig) => SubscriptionResult
  unsubscribe: (clientId: string, subscriptionId: string) => UnsubscribeResult
  unsubscribeAll: (clientId: string) => UnsubscribeResult
  publish: (
    eventType: EventType,
    data: unknown,
    channel?: EventChannel,
    metadata?: Record<string, unknown>
  ) => PublishResult
  getStats: () => EventStats
  getActiveClients: () => ClientInfo[]
} => {
  const socketClient = getSocketClient()
  const pubSubManager = getPubSubManager()

  return {
    socketClient,
    pubSubManager,
    connect: () => socketClient.connect(),
    disconnect: () => socketClient.disconnect(),
    registerClient: (windowId: number, metadata?: Record<string, unknown>) =>
      pubSubManager.registerClient(windowId, metadata),
    subscribe: (config: SubscriptionConfig) => pubSubManager.subscribe(config),
    unsubscribe: (clientId: string, subscriptionId: string) =>
      pubSubManager.unsubscribe(clientId, subscriptionId),
    unsubscribeAll: (clientId: string) => pubSubManager.unsubscribeAll(clientId),
    publish: (
      eventType: EventType,
      data: unknown,
      channel?: EventChannel,
      metadata?: Record<string, unknown>
    ) => pubSubManager.publish(eventType, data, channel, metadata),
    getStats: () => pubSubManager.getStats(),
    getActiveClients: () => pubSubManager.getActiveClients()
  }
}
