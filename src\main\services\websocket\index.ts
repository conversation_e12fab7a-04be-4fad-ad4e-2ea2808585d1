/**
 * WebSocket Service Exports
 * Central export file for all WebSocket and pub-sub related functionality
 */

// Core classes
export { default as SocketClient } from './SocketClient'
export { PubSubManager } from './PubSubManager'
export { EventHandlers } from './EventHandlers'

// Enums and constants
export { AccountType, SocketStatus, SocketURLs, Auth } from './enums'

// Types and interfaces
export * from './types/pubsub'

// Utility functions
export { formatData } from '../../../shared/utils/formatter'

/**
 * Convenience function to get the singleton instances
 */
export const getSocketClient = () => SocketClient.getInstance()
export const getPubSubManager = () => PubSubManager.getInstance()

/**
 * Initialize the WebSocket service with pub-sub system
 */
export const initializeWebSocketService = () => {
  const socketClient = getSocketClient()
  const pubSubManager = getPubSubManager()
  
  return {
    socketClient,
    pubSubManager,
    connect: () => socketClient.connect(),
    disconnect: () => socketClient.disconnect(),
    registerClient: (windowId: number, metadata?: Record<string, unknown>) => 
      pubSubManager.registerClient(windowId, metadata),
    subscribe: (config: any) => pubSubManager.subscribe(config),
    unsubscribe: (clientId: string, subscriptionId: string) => 
      pubSubManager.unsubscribe(clientId, subscriptionId),
    unsubscribeAll: (clientId: string) => pubSubManager.unsubscribeAll(clientId),
    publish: (eventType: any, data: unknown, channel?: any, metadata?: Record<string, unknown>) => 
      pubSubManager.publish(eventType, data, channel, metadata),
    getStats: () => pubSubManager.getStats(),
    getActiveClients: () => pubSubManager.getActiveClients()
  }
}
