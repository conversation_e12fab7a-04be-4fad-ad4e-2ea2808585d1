# WebSocket Pub-Sub Migration Guide

This guide explains how to migrate from the old broadcast system to the new publish-subscribe pattern in the WebSocket service.

## Overview

The WebSocket service has been upgraded from a simple broadcast mechanism to a sophisticated publish-subscribe (pub-sub) system that provides:

- **Selective messaging**: Clients only receive events they're subscribed to
- **Event filtering**: Advanced filtering capabilities for fine-grained control
- **Performance improvements**: Reduced unnecessary message delivery
- **Better organization**: Events are categorized by type and channel
- **Memory management**: Automatic cleanup of inactive clients and subscriptions

## Key Changes

### Before (Old Broadcast System)
```typescript
// Old way - all windows received all events
socketClient.broadcast('updateCharts', chartData)

// All browser windows would receive this event regardless of interest
```

### After (New Pub-Sub System)
```typescript
// New way - only subscribed clients receive events
pubSubManager.publish(EventType.CHARTS_UPDATE, chartData, EventChannel.TRADING)

// Only clients subscribed to CHARTS_UPDATE or TRADING channel receive this
```

## Migration Steps

### 1. Update Imports

**Before:**
```typescript
import Socket<PERSON>lient from './services/websocket/SocketClient'
```

**After:**
```typescript
import { 
  SocketClient, 
  PubSubManager, 
  EventType, 
  EventChannel,
  initializeWebSocketService 
} from './services/websocket'
```

### 2. Initialize the Service

**Before:**
```typescript
const socketClient = SocketClient.getInstance()
await socketClient.connect()
```

**After:**
```typescript
const webSocketService = initializeWebSocketService()
await webSocketService.connect()

// Or use individual components
const socketClient = SocketClient.getInstance()
const pubSubManager = PubSubManager.getInstance()
```

### 3. Register Clients (New Requirement)

In the new system, each browser window must be registered as a client:

```typescript
// In your main process when creating windows
const clientId = pubSubManager.registerClient(window.id, {
  windowType: 'main',
  userId: 'user123'
})
```

### 4. Subscribe to Events

**Before:**
```typescript
// Old system - all windows received all events automatically
// No subscription mechanism existed
```

**After:**
```typescript
// Subscribe to specific events
const subscription = pubSubManager.subscribe({
  clientId: 'client_123',
  eventType: EventType.BALANCE_UPDATE,
  channel: EventChannel.ACCOUNT
})

// Subscribe to all events in a channel
const channelSubscription = pubSubManager.subscribe({
  clientId: 'client_123',
  channel: EventChannel.TRADING
})

// Subscribe with filtering
const filteredSubscription = pubSubManager.subscribe({
  clientId: 'client_123',
  eventType: EventType.CHARTS_UPDATE,
  filter: {
    dataFilter: { symbol: 'EURUSD' }
  }
})
```

### 5. Publishing Events

**Before:**
```typescript
socketClient.broadcast('updateBalance', balanceData)
socketClient.broadcast('updateCharts', chartData)
```

**After:**
```typescript
// Use specific event types
pubSubManager.publish(EventType.BALANCE_UPDATE, balanceData)
pubSubManager.publish(EventType.CHARTS_UPDATE, chartData)

// With channel specification
pubSubManager.publish(
  EventType.CHARTS_UPDATE, 
  chartData, 
  EventChannel.TRADING,
  { symbol: 'EURUSD', timeframe: '1m' }
)
```

### 6. Receiving Events in Renderer

**Before:**
```typescript
// Old way - received all events
window.api.on('ws:event', (event, data) => {
  if (event === 'updateBalance') {
    // Handle balance update
  }
})
```

**After:**
```typescript
// New way - structured event data
window.api.on('ws:event', (eventType, data, metadata) => {
  switch (eventType) {
    case 'account:balance':
      handleBalanceUpdate(data, metadata)
      break
    case 'trading:charts':
      handleChartsUpdate(data, metadata)
      break
  }
})
```

### 7. Cleanup

**Before:**
```typescript
// No cleanup was needed
```

**After:**
```typescript
// Clean up subscriptions when window closes
window.addEventListener('beforeunload', () => {
  pubSubManager.unsubscribeAll(clientId)
})

// Or remove specific subscriptions
pubSubManager.unsubscribe(clientId, subscriptionId)
```

## Event Types and Channels

### Available Event Types
```typescript
enum EventType {
  // Authentication
  AUTH_SUCCESS = 'auth:success',
  AUTH_FAILED = 'auth:failed',
  
  // Account
  BALANCE_UPDATE = 'account:balance',
  ACCOUNT_STATUS = 'account:status',
  
  // Trading
  CHARTS_UPDATE = 'trading:charts',
  HISTORY_UPDATE = 'trading:history',
  HISTORY_LOAD = 'trading:history_load',
  STREAM_UPDATE = 'trading:stream',
  ASSETS_UPDATE = 'trading:assets',
  
  // System
  CONNECTION_STATUS = 'system:connection',
  HEARTBEAT = 'system:heartbeat',
  ERROR = 'system:error'
}
```

### Available Channels
```typescript
enum EventChannel {
  AUTHENTICATION = 'auth',
  ACCOUNT = 'account',
  TRADING = 'trading',
  SYSTEM = 'system',
  ALL = '*' // Subscribe to all events
}
```

## Advanced Features

### Event Filtering
```typescript
// Filter by data properties
pubSubManager.subscribe({
  clientId: 'client_123',
  eventType: EventType.CHARTS_UPDATE,
  filter: {
    dataFilter: { symbol: 'EURUSD', timeframe: '1h' }
  }
})

// Custom filter function
pubSubManager.subscribe({
  clientId: 'client_123',
  eventType: EventType.BALANCE_UPDATE,
  filter: {
    customFilter: (payload) => payload.data.balance > 1000
  }
})
```

### Priority Subscriptions
```typescript
pubSubManager.subscribe({
  clientId: 'client_123',
  eventType: EventType.ERROR,
  priority: SubscriptionPriority.CRITICAL
})
```

### Statistics and Monitoring
```typescript
// Get system statistics
const stats = pubSubManager.getStats()
console.log('Active subscriptions:', stats.activeSubscriptions)
console.log('Active clients:', stats.activeClients)
console.log('Events by type:', stats.eventsByType)

// Get client information
const clientInfo = pubSubManager.getClientInfo(clientId)
const activeClients = pubSubManager.getActiveClients()
```

## Backward Compatibility

The old `broadcast()` method is still available but deprecated:

```typescript
// This still works but is deprecated
socketClient.broadcast('updateBalance', data)

// It internally converts to:
pubSubManager.publish(EventType.BALANCE_UPDATE, data)
```

## Performance Benefits

1. **Reduced Network Traffic**: Only interested clients receive events
2. **Lower CPU Usage**: No unnecessary event processing
3. **Memory Efficiency**: Automatic cleanup of inactive subscriptions
4. **Scalability**: Better performance with multiple windows/clients

## Error Handling

```typescript
// Subscribe with error handling
const result = pubSubManager.subscribe({
  clientId: 'client_123',
  eventType: EventType.BALANCE_UPDATE
})

if (!result.success) {
  console.error('Subscription failed:', result.error)
}

// Publish with callback
pubSubManager.publish(
  EventType.CHARTS_UPDATE,
  data,
  EventChannel.TRADING,
  undefined,
  (result, payload) => {
    if (!result.success) {
      console.error('Publish failed:', result.error)
    } else {
      console.log(`Delivered to ${result.deliveredTo.length} clients`)
    }
  }
)
```

## Best Practices

1. **Register clients early**: Register clients as soon as windows are created
2. **Subscribe selectively**: Only subscribe to events you actually need
3. **Clean up subscriptions**: Always unsubscribe when clients disconnect
4. **Use appropriate channels**: Group related events using channels
5. **Monitor performance**: Use the statistics API to monitor system health
6. **Handle errors gracefully**: Always check operation results

## Troubleshooting

### Common Issues

1. **Events not received**: Ensure client is registered and subscribed
2. **Memory leaks**: Make sure to call `unsubscribeAll()` on client disconnect
3. **Performance issues**: Check subscription counts and use filtering
4. **Type errors**: Import types from the correct module

### Debug Information

```typescript
// Enable debug logging
const logger = new Logger({ prefix: 'PubSub', minLevel: LoggerLevel.DEBUG })

// Check active subscriptions
const subscriptions = pubSubManager.getClientSubscriptions(clientId)
console.log('Client subscriptions:', subscriptions)

// Monitor events
pubSubManager.publish(EventType.CUSTOM, { debug: true })
```
