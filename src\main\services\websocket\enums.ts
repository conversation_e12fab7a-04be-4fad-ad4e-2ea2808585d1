export enum AccountType {
  Demo = 'Demo',
  Live = 'Live'
}

export enum SocketStatus {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  AUTHENTICATING = 'AUTHENTICATING',
  AUTHENTICATED = 'AUTHENTICATED',
  ERROR = 'ERROR'
}

export enum SocketURLs {
  DEMO = 'wss://demo-api-eu.po.market',
  LIVE = '',
  ORIGIN = 'https://pocketoption.com'
}

export const Auth = {
  SESSION_ID: import.meta.env.MAIN_VITE_SESSION_ID,
  USER_ID: import.meta.env.MAIN_VITE_USER_ID
} as const
